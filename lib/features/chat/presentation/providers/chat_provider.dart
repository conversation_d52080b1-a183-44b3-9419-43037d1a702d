import 'dart:async';

import 'package:event_bus/event_bus.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/extensions/list_ext.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/chat/data/message/message_local_datasource.dart';
import 'package:flutter_audio_room/features/chat/data/repositories/message_local_repository.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/ephemeral_message_extension.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/message_metadata_extension.dart';
import 'package:flutter_audio_room/features/chat/domain/repositories/i_message_repository.dart';
import 'package:flutter_audio_room/features/chat/presentation/controllers/chat_socket_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/base/chat_provider_base.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/conversation_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/mixin/receive_message_mixin.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/mixin/send_message_mixin.dart';
import 'package:flutter_audio_room/services/file_service/i_file_service.dart';
import 'package:flutter_audio_room/services/message_expiry_service/events/message_expiry_events.dart';
import 'package:flutter_audio_room/services/message_expiry_service/message_expiry_isolate_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final chatProvider =
    AsyncNotifierProvider.family<ChatNotifier, List<Message>, String>(
  ChatNotifier.new,
);

class ChatNotifier extends ChatProviderBase
    with SendMessageMixin, ReceiveMessageMixin {
  late IMessageRepository _messageRepository;
  final IFileService _fileService = getIt();
  final EventBus _eventBus = getIt();
  StreamSubscription? _expiredMessagesSubscription;
  Timer? _expiryCheckDebounceTimer;

  @override
  IMessageRepository get messageLocalRepository => _messageRepository;

  @override
  String get conversationId => arg;

  @override
  IFileService get fileService => _fileService;

  int _currentPage = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  bool get hasMore => _hasMore;

  @override
  Future<List<Message>> build(String arg) async {
    final localUserId = ref.read(accountProvider).userInfo?.profile?.id;
    _messageRepository = MessageLocalRepository(
      localDataSource: MessageLocalDatasource(
        conversationId: arg,
        userId: localUserId ?? '',
      ),
    );
    
    // 初始加载最后一页
    final result = await _messageRepository.getMessages(
      limit: _pageSize,
      offset: 0,
    );

    // 监听过期消息删除事件
    _setupExpiredMessagesListener();

    // Future.delayed(const Duration(seconds: 3), () async {
    //   for (var i = 0; i < 100; i++) {
    //     final createdAt = DateTime.now().millisecondsSinceEpoch;
    //     await addMessage(TextMessage(
    //       id: i.toString(),
    //       text: i.toString(),
    //       author: User(id: localUserId ?? ''),
    //       type: MessageType.text,
    //       createdAt: createdAt,
    //     ));
    //   }
    // });

    ref.onDispose(() {
      _disposeExpiredMessagesListener();
    });

    return result.fold(
      (left) {
        LogUtils.e('Error fetching messages: $left', tag: 'ChatProvider');
        return [];
      },
      (right) {
        LogUtils.d('Get local messages: ${right.length}', tag: 'ChatProvider');
        _hasMore = right.length >= _pageSize;
        return right;
      },
    );
  }

  Future<bool> loadMore() async {
    if (!_hasMore) return false;

    final messageList = await future;

    _currentPage++;
    final result = await _messageRepository.getMessages(
      limit: _pageSize,
      offset: _currentPage * _pageSize,
    );

    return result.fold(
      (left) {
        LogUtils.e('Error loading more: $left', tag: 'ChatProvider');
        return false;
      },
      (right) {
        if (right.isEmpty) {
          _hasMore = false;
          return false;
        }
        state = AsyncData([
          ...messageList,
          ...right,
        ]);
        _hasMore = right.length >= _pageSize;
        return _hasMore;
      },
    );
  }
  
  /// 设置过期消息删除事件监听
  void _setupExpiredMessagesListener() {
    _disposeExpiredMessagesListener(); // 确保没有重复的监听器

    _expiredMessagesSubscription =
        _eventBus.on<ExpiredMessagesDeletedEvent>().listen((event) {
      // 只处理当前会话的过期消息删除事件
      if (event.conversationId == conversationId) {
        _handleExpiredMessagesDeleted(event.deletedMessageIds);
      }
    });

    LogUtils.d(
        'Expired messages listener setup for conversation: $conversationId',
        tag: 'ChatProvider');
  }

  /// 清理过期消息监听器资源
  void _disposeExpiredMessagesListener() {
    _expiredMessagesSubscription?.cancel();
    _expiredMessagesSubscription = null;
    _expiryCheckDebounceTimer?.cancel();
    _expiryCheckDebounceTimer = null;
    LogUtils.d('Expired messages listener disposed', tag: 'ChatProvider');
  }

  /// 处理过期消息删除事件
  void _handleExpiredMessagesDeleted(List<String> deletedMessageIds) {
    if (deletedMessageIds.isEmpty) return;

    // 从内存中的消息列表中移除已删除的消息
    final currentMessages = state.value ?? [];
    final updatedMessages = currentMessages
        .where((message) => !deletedMessageIds.contains(message.id))
        .toList();

    if (updatedMessages.length != currentMessages.length) {
      state = AsyncValue.data(updatedMessages);
      LogUtils.d(
          'Removed ${currentMessages.length - updatedMessages.length} expired messages from memory',
          tag: 'ChatProvider');
    }
  }

  Future<ResultWithData<Message?>> getLastMessage() async {
    final messageList = await future;
    if (messageList.isEmpty) {
      return const Left(
        AppException(
            message: 'Message not find',
            statusCode: 400,
            identifier: 'getLastMessage'),
      );
    }
    final lastMessage = messageList.lastWhereOrNull((m) => m.remoteId != null);
    return Right(lastMessage);
  }

  Future<ResultWithData<List<Message>>> getReceiveUnreadMessages() async {
    final messageList = await future;
    if (messageList.isEmpty) {
      return const Left(
        AppException(
          message: 'Message not find',
          statusCode: 400,
          identifier: 'getUnreadMessages',
        ),
      );
    }
    final unreadMessages = messageList
        .where((e) => e.status != Status.seen && e.author.id == conversationId)
        .toList();

    // Sort by creation time (ascending) to ensure proper read order for ephemeral messages
    unreadMessages
        .sort((a, b) => (a.createdAt ?? 0).compareTo(b.createdAt ?? 0));

    return Right(unreadMessages);
  }

  Future<ResultWithData<List<Message>>> getSentUnreadMessages() async {
    final messageList = await future;
    if (messageList.isEmpty) {
      return const Left(
        AppException(
          message: 'Message not find',
          statusCode: 400,
          identifier: 'getSentUnreadMessages',
        ),
      );
    }
    final unreadMessages = messageList
        .where((e) => e.status != Status.seen && e.author.id != conversationId)
        .toList();

    // Sort by creation time (ascending) to ensure proper read order for ephemeral messages
    unreadMessages
        .sort((a, b) => (a.createdAt ?? 0).compareTo(b.createdAt ?? 0));

    return Right(unreadMessages);
  }

  @override
  Future<ResultWithData<Message>> addMessage(
    Message message,
  ) async {
    final messageList = await future;

    final result = await _messageRepository.createMessage(message);
    return result.fold(
      (left) {
        // Handle error
        LogUtils.d('Error saving message: $left', tag: 'addMessage');
        return Left(left);
      },
      (right) {
        // Handle success
        if (messageList.any((e) => e.id == right.id)) {
          return Right(right);
        }

        state = AsyncData([right, ...messageList]);

        // 如果是已读的阅后即焚消息，通知过期检查服务
        if (right.isEphemeral && right.readAt != null) {
          _notifyEphemeralMessageRead(right);
        }

        return Right(right);
      },
    );
  }

  @override
  Future<void> updateMessage(Message message) async {
    final messageList = await future;
    final hasMatch = messageList.any((e) => e.id == message.id);
    LogUtils.d(
        '${message.id} hasMatch: $hasMatch, ',
        tag: 'updateMessage');

    // 先更新数据库，确保持久化成功
    try {
      await messageLocalRepository.updateMessage(message);
      LogUtils.d('Successfully updated message in database: ${message.id}',
          tag: 'updateMessage');

      // 数据库更新成功后，再更新内存状态
      final updatedMessages = messageList.map((e) {
        if (message.id == e.id) {
          return message;
        }
        return e;
      }).toList();
      state = AsyncData(updatedMessages);

    } catch (e) {
      LogUtils.e(
          'Failed to update message in database: ${message.id}, error: $e',
          tag: 'updateMessage');
      // 数据库更新失败时，不更新内存状态
      rethrow;
    }
  }

  @override
  Future<void> clearMessages() async {
    state = const AsyncData([]);
    await _messageRepository.clearMessages();
  }

  Future<void> deleteMessages(List<Message> messages) async {
    final messageList = await future;
    if (messageList.isEmpty) {
      return;
    }

    final updatedMessages =
        messageList.where((e) => !messages.any((m) => m.id == e.id)).toList();
    state = AsyncData(updatedMessages);

    for (final message in messages) {
      await messageLocalRepository.deleteMessage(message.id);
    }

    final lastMessage = updatedMessages.lastOrNull;
    await ref
          .read(conversationProvider.notifier)
        .updateLastMessage(conversationId, lastMessage);
  }

  @override
  Future<void> deleteMessage(Message message) async {
    return deleteMessages([message]);
  }

  Future<void> syncAllOfflineMsg({
    required String conversationId,
    required int startTimeStamp,
    required Function(dynamic) ack,
  }) async {
    await ref.read(chatSocketProvider.notifier).emitWithAck(
          ChatSocketEvents.syncAllOfflineMsg,
          {
            'startTimeStamp': startTimeStamp,
            'conversationId': conversationId,
          },
          ack,
        );
  }

  /// 将消息标记为已读，并设置阅后即焚计时
  @override
  Future<void> setMessageAsRead(String messageId) async {
    await batchSetMessagesAsRead([messageId]);
  }

  /// 批量将消息标记为已读，使用相同的时间戳确保正确的过期顺序
  Future<void> batchSetMessagesAsRead(List<String> messageIds,
      {DateTime? readTime}) async {
    if (messageIds.isEmpty) return;

    final messageList = await future;
    if (messageList.isEmpty) {
      return;
    }

    // 使用统一的读取时间，确保批量处理的消息有相同的基准时间
    final unifiedReadTime = readTime ?? DateTime.now();
    final readTimestamp = unifiedReadTime.millisecondsSinceEpoch;

    final messagesToUpdate = <Message>[];
    final ephemeralMessagesToNotify = <Message>[];

    // 收集需要更新的消息
    for (final messageId in messageIds) {
      var messageToUpdate = messageList.cast<Message?>().firstWhere(
            (message) =>
                (message?.remoteId == messageId || message?.id == messageId) &&
                ![Status.error, Status.seen].contains(message?.status),
            orElse: () => null,
          );

      if (messageToUpdate == null) {
        final result = await _messageRepository.getMessage(messageId);
        if (result.isRight()) {
          messageToUpdate = result.getRight();
        }
      }

      if (messageToUpdate == null || messageToUpdate.status == Status.seen) {
        continue;
      }

      final updatedMessage = messageToUpdate.copyWith(
        status: Status.seen,
        updatedAt: readTimestamp,
      );

      // 如果是阅后即焚消息，使用统一时间标记为已读
      if (updatedMessage.isEphemeral) {
        final newExtend = updatedMessage.extend.copyWith(readAt: readTimestamp);
        final markedMessage =
            updatedMessage.copyWith(metadata: newExtend.toJson());
        messagesToUpdate.add(markedMessage);
        ephemeralMessagesToNotify.add(markedMessage);
      } else {
        messagesToUpdate.add(updatedMessage);
      }
    }

    if (messagesToUpdate.isEmpty) return;

    // 批量更新数据库
    for (final message in messagesToUpdate) {
      try {
        await messageLocalRepository.updateMessage(message);
      } catch (e) {
        LogUtils.e('Failed to update message ${message.id}: $e',
            tag: 'batchSetMessagesAsRead');
      }
    }

    // 批量更新内存状态
    final updatedMessageList = messageList.map((e) {
      final updatedMessage =
          messagesToUpdate.firstWhereOrNull((updated) => updated.id == e.id);
      return updatedMessage ?? e;
    }).toList();

    state = AsyncData(updatedMessageList);

    // 批量通知过期检查服务
    for (final message in ephemeralMessagesToNotify) {
      _notifyEphemeralMessageRead(message);
    }

    LogUtils.d(
        'Batch updated ${messagesToUpdate.length} messages as read with unified timestamp: $readTimestamp',
        tag: 'ChatProvider');
  }

  Future<void> checkAndDeleteExpiredMessages() async {
    // 复用批量删除逻辑，避免实时检查时的UI抖动
    final messageList = await future;
    if (messageList.isEmpty) {
      return;
    }

    await batchDeleteExpiredMessages(messageList);
  }

  Future<void> batchDeleteExpiredMessages(List<Message> messageList) async {
    if (messageList.isEmpty) {
      return;
    }

    // 查找过期消息
    final expiredMessages = messageList
        .where((message) =>
            message.isEphemeral && message.readAt != null && message.isExpired)
        .toList();

    // 如果没有过期消息，直接返回，不更新状态
    if (expiredMessages.isEmpty) {
      return;
    }

    await deleteMessages(expiredMessages);
  }

  /// 通知过期检查服务有阅后即焚消息被标记为已读
  void _notifyEphemeralMessageRead(Message message) {
    try {
      if (!message.isEphemeral || message.readAt == null) {
        return;
      }

      final expiryService = MessageExpiryIsolateService.instance;
      final readTime = message.readAt!;
      final timeoutSeconds = message.ephemeralTimeout ?? 1;

      expiryService.notifyEphemeralMessageRead(
        conversationId: conversationId,
        messageId: message.id,
        readTime: readTime,
        timeoutSeconds: timeoutSeconds,
      );

      LogUtils.d(
          'Notified expiry service: message ${message.id} read at $readTime, expires in ${timeoutSeconds}s',
          tag: 'ChatProvider');
    } catch (e) {
      LogUtils.e('Error notifying ephemeral message read: $e',
          tag: 'ChatProvider');
    }
  }
}
