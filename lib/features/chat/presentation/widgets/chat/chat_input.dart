import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/instant_chat/presentation/provider/instant_call_provider.dart';
import 'package:flutter_audio_room/features/voice_call/friend/provider/friend_voice_call_provider.dart';
import 'package:flutter_audio_room/gen/assets.gen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 聊天输入组件，支持文本输入和语音录制
class ChatInput extends ConsumerStatefulWidget {
  final TextEditingController textController;
  final FocusNode focusNode;
  final int maxTextLength;
  final Function() onSendText;
  final String conversationId;

  const ChatInput({
    super.key,
    required this.textController,
    required this.focusNode,
    required this.maxTextLength,
    required this.onSendText,
    required this.conversationId,
  });

  @override
  ConsumerState<ChatInput> createState() => _ChatInputState();
}

class _ChatInputState extends ConsumerState<ChatInput> {
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    // 初始化时检查文本状态
    _hasText = widget.textController.text.isNotEmpty;
    // 添加监听器，当文本发生变化时更新状态
    widget.textController.addListener(_updateTextStatus);
  }

  @override
  void dispose() {
    // 移除监听器，避免内存泄漏
    widget.textController.removeListener(_updateTextStatus);
    super.dispose();
  }

  /// 更新文本状态
  void _updateTextStatus() {
    final bool hasText = widget.textController.text.isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  void _onStartCall() async {
    final inAudioRoom = ref
        .read(audioRoomProvider.select((state) => state.currentRoom != null));
    final isInInstantCall =
        ref.read(instantCallProvider.select((state) => state.isCallActive));

    final isInFriendCall =
        ref.read(friendVoiceCallProvider.select((state) => state.isCallActive));

    if (inAudioRoom) {
      LoadingUtils.showToast('You are already in a audio room');
      return;
    }

    if (isInInstantCall) {
      LoadingUtils.showToast('You are already in a call');
      return;
    }

    if (isInFriendCall) {
      LoadingUtils.showToast('You are already in a friend call');
      return;
    }

    ref.read(friendVoiceCallProvider.notifier).startCall(widget.conversationId);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: EdgeInsets.only(bottom: 10.h),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        child: Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: context.theme.colorScheme.onSurface
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(24.r),
                ),
                child: _buildTextInputMode(context),
              ),
            ),
            16.horizontalSpace,
            AppButton(
              type: AppButtonType.contained,
              backgroundColor: context.theme.colorScheme.surface.withValues(
                alpha: 0.3,
              ),
              borderRadius: 999,
              onPressed: _onStartCall,
              padding: EdgeInsets.all(1.w),
              child: Assets.images.callMic.image(width: 33.w, height: 33.h),
            ),
            16.horizontalSpace,
            AppButton(
              type: AppButtonType.contained,
              onPressed: () {
                // TODO: 打开礼物选择页面
              },
              padding: EdgeInsets.all(1.w),
              borderRadius: 999,
              backgroundColor: context.theme.colorScheme.surface.withValues(
                alpha: 0.3,
              ),
              child: Assets.images.gift.image(width: 33.w, height: 33.h),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建文本输入模式
  Widget _buildTextInputMode(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: widget.textController,
            focusNode: widget.focusNode,
            maxLength: widget.maxTextLength,
            textInputAction: TextInputAction.send,
            decoration: InputDecoration(
              hintText: 'Input message...',
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
              counter: const SizedBox(),
              hintStyle: TextStyle(
                color:
                    context.theme.colorScheme.onSurface.withValues(alpha: 0.5),
                fontSize: 16.sp,
              ),
            ),
            style: TextStyle(
              color: context.theme.colorScheme.onSurface,
              fontSize: 16.sp,
            ),
            onSubmitted: (text) {
              if (text.isNotEmpty) {
                widget.onSendText();
              }
            },
          ),
        ),
        AppButton(
          type: AppButtonType.contained,
          onPressed: _hasText ? widget.onSendText : null,
          throttleDuration: const Duration(milliseconds: 200),
          child: Icon(
            Icons.send,
            color: _hasText
              ? context.theme.colorScheme.primary
                : context.theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
        ),
      ],
    );
  }
}
