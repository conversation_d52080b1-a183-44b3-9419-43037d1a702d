import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/chat/presentation/controllers/chat_socket_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/chat_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

mixin MessageReadHandleMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  Future<void> onReadMessage() async {
    if (!mounted) return;
    await ref
        .read(chatSocketProvider.notifier)
        .listenToEvent(ChatSocketListenEvents.messageRead,
        (res) async {
      LogUtils.d('messageRead: $res', tag: 'ChatSocket');
      final conversationId = res['conversationId'] as String;
      // final messageId = res['msgId'] as String;

      final sentUnreadMessageResult = await ref
          .read(chatProvider(conversationId).notifier)
          .getSentUnreadMessages();
      
      final sentUnreadMessages = sentUnreadMessageResult.getRight() ?? [];

      // 批量处理已读消息，使用统一时间戳确保正确的过期顺序
      if (sentUnreadMessages.isNotEmpty) {
        final messageIds = sentUnreadMessages.map((m) => m.id).toList();
        await ref
            .read(chatProvider(conversationId).notifier)
            .batchSetMessagesAsRead(messageIds);
      }
    });
  }
}