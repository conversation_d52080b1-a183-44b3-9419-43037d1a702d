import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/chat/domain/interface/message_datasource.dart';
import 'package:flutter_audio_room/services/isar_service/i_isar_service.dart';
import 'package:flutter_audio_room/services/isar_service/message/isar_message_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart';

class MessageLocalDatasource implements MessageDataSource {
  final String conversationId;
  final String userId;
  final _isarService = IsarMessageService(
    (getIt<IIsarService>()).isar,
  );

  MessageLocalDatasource({
    required this.conversationId,
    required this.userId,
  });

  @override
  Future<ResultWithData<List<Message>>> getMessages({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final messages = await _isarService.queryMessages(
        conversationId: conversationId,
        userId: userId,
        limit: limit,
        offset: offset,
      );
      return Right(messages);
    } catch (e) {
      LogUtils.e('Failed to get messages: $e', tag: 'MessageLocalDatasource');
      return Left(AppException(
        identifier: 'get_messages_failed',
        message: e.toString(),
        statusCode: 500,
      ));
    }
  }

  @override
  Future<ResultWithData<Message>> getMessage(String messageId) async {
    try {
      final message = await _isarService.getMessage(messageId);
      if (message == null) {
        return const Left(AppException(
          identifier: 'message_not_found',
          message: 'Message not found',
          statusCode: 404,
        ));
      }
      return Right(message);
    } catch (e) {
      LogUtils.e('Failed to get message: $e', tag: 'MessageLocalDatasource');
      return Left(AppException(
        identifier: 'get_message_failed',
        message: e.toString(),
        statusCode: 500,
      ));
    }
  }

  @override
  Future<ResultWithData<Message>> createMessage(Message message) async {
    try {
      await _isarService.createMessage(
        message,
        conversationId: conversationId,
        userId: userId,
      );
      return Right(message);
    } catch (e) {
      LogUtils.e('Failed to create message: $e', tag: 'MessageLocalDatasource');
      return Left(AppException(
        identifier: 'create_message_failed',
        message: e.toString(),
        statusCode: 500,
      ));
    }
  }

  @override
  Future<VoidResult> updateMessage(Message message) async {
    try {
      await _isarService.updateMessage(
        message,
      );
      return const Right(null);
    } catch (e) {
      LogUtils.e('Failed to update message: $e', tag: 'MessageLocalDatasource');
      return Left(AppException(
        identifier: 'update_message_failed',
        message: e.toString(),
        statusCode: 500,
      ));
    }
  }

  @override
  Future<VoidResult> deleteMessage(String messageId) async {
    try {
      await _isarService.deleteMessage(messageId);
      return const Right(null);
    } catch (e) {
      LogUtils.e('Failed to delete message: $e', tag: 'MessageLocalDatasource');
      return Left(AppException(
        identifier: 'delete_message_failed',
        message: e.toString(),
        statusCode: 500,
      ));
    }
  }

  @override
  Future<VoidResult> clearMessages() async {
    try {
      await _isarService.clearConversationMessages(conversationId, userId);
      return const Right(null);
    } catch (e) {
      LogUtils.e('Failed to clear messages: $e', tag: 'MessageLocalDatasource');
      return Left(AppException(
        identifier: 'clear_messages_failed',
        message: e.toString(),
        statusCode: 500,
      ));
    }
  }
}
