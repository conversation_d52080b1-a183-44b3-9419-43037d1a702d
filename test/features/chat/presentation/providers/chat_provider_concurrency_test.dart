import 'dart:async';

import 'package:flutter_audio_room/features/chat/presentation/providers/chat_provider.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ChatProvider Concurrency Tests', () {
    late ProviderContainer container;
    const testConversationId = 'test_conversation_id';

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('concurrent addMessage calls should not lose messages', () async {
      // 这个测试验证并发调用 addMessage 时不会丢失消息
      final chatNotifier = container.read(chatProvider(testConversationId).notifier);
      
      // 创建多个测试消息
      final messages = List.generate(10, (index) {
        final now = DateTime.now().millisecondsSinceEpoch;
        return TextMessage(
          id: 'test_message_$index',
          text: 'Test message $index',
          author: const User(id: 'test_user'),
          type: MessageType.text,
          createdAt: now + index, // 确保每个消息有不同的时间戳
        );
      });

      // 并发添加消息
      final futures = messages.map((message) async {
        // 添加一些随机延迟来模拟真实的并发场景
        await Future.delayed(Duration(milliseconds: (message.text.hashCode % 50)));
        return chatNotifier.addMessage(message);
      }).toList();

      // 等待所有操作完成
      final results = await Future.wait(futures);

      // 验证所有消息都成功添加
      expect(results.length, equals(10));
      for (final result in results) {
        expect(result.isRight(), isTrue, reason: 'All messages should be added successfully');
      }

      // 验证最终状态包含所有消息
      final finalState = await container.read(chatProvider(testConversationId).future);
      expect(finalState.length, equals(10), reason: 'All messages should be present in final state');

      // 验证消息ID的唯一性
      final messageIds = finalState.map((m) => m.id).toSet();
      expect(messageIds.length, equals(10), reason: 'All message IDs should be unique');
    });

    test('concurrent updateMessage calls should not lose updates', () async {
      final chatNotifier = container.read(chatProvider(testConversationId).notifier);
      
      // 首先添加一个基础消息
      final baseMessage = TextMessage(
        id: 'base_message',
        text: 'Base message',
        author: const User(id: 'test_user'),
        type: MessageType.text,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        status: Status.sending,
      );

      await chatNotifier.addMessage(baseMessage);

      // 并发更新消息状态
      final updateFutures = List.generate(5, (index) async {
        await Future.delayed(Duration(milliseconds: index * 10));
        final updatedMessage = baseMessage.copyWith(
          status: index == 4 ? Status.sent : Status.sending,
          updatedAt: DateTime.now().millisecondsSinceEpoch + index,
        );
        return chatNotifier.updateMessage(updatedMessage);
      });

      // 等待所有更新完成
      await Future.wait(updateFutures);

      // 验证最终状态
      final finalState = await container.read(chatProvider(testConversationId).future);
      expect(finalState.length, equals(1), reason: 'Should still have only one message');
      
      final finalMessage = finalState.first;
      expect(finalMessage.id, equals('base_message'));
      // 最后一次更新应该是 Status.sent
      expect(finalMessage.status, equals(Status.sent));
    });

    test('mixed concurrent operations should maintain consistency', () async {
      final chatNotifier = container.read(chatProvider(testConversationId).notifier);
      
      // 创建混合操作：添加、更新、删除
      final operations = <Future<void>>[];

      // 添加一些消息
      for (int i = 0; i < 5; i++) {
        operations.add(Future(() async {
          await Future.delayed(Duration(milliseconds: i * 5));
          final message = TextMessage(
            id: 'message_$i',
            text: 'Message $i',
            author: const User(id: 'test_user'),
            type: MessageType.text,
            createdAt: DateTime.now().millisecondsSinceEpoch + i,
          );
          await chatNotifier.addMessage(message);
        }));
      }

      // 等待所有操作完成
      await Future.wait(operations);

      // 验证最终状态的一致性
      final finalState = await container.read(chatProvider(testConversationId).future);
      expect(finalState.length, equals(5), reason: 'Should have 5 messages');

      // 验证消息顺序（最新的在前面）
      for (int i = 0; i < finalState.length - 1; i++) {
        final current = finalState[i];
        final next = finalState[i + 1];
        expect(current.createdAt! >= next.createdAt!, isTrue, 
               reason: 'Messages should be ordered by creation time (newest first)');
      }
    });
  });
}
